import{d as _,f as u,j as b,c as d,a as t,t as s,k as g,h as v,l as i,v as r,m as y,i as f,n as $,o as m,_ as k}from"./index-Ql5_j0uM.js";const w={class:"contact-view"},V={class:"contact-hero section"},j={class:"container"},M={class:"hero-content"},S={class:"hero-title"},q={class:"hero-subtitle"},C={class:"contact-content section"},T={class:"container"},U={class:"contact-grid"},E={class:"contact-form-section"},P={class:"form-group"},B={for:"name"},D=["placeholder"],I={class:"form-group"},N={for:"email"},R=["placeholder"],z={class:"form-group"},A={for:"company"},F=["placeholder"],G={class:"form-group"},L={for:"phone"},O=["placeholder"],W={class:"form-group"},H={for:"subject"},J={value:""},K={class:"form-group"},Q={for:"message"},X=["placeholder"],Y=["disabled"],Z={key:0},x={key:1},tt={key:2,class:"btn-icon",width:"20",height:"20",viewBox:"0 0 24 24",fill:"none"},ot={class:"contact-info-section"},et={class:"contact-methods"},st={class:"contact-method"},at={class:"method-content"},nt={class:"contact-method"},lt={class:"method-content"},it={class:"contact-method"},ct={class:"method-content"},dt=_({__name:"ContactView",setup(rt){const c=u(!1),a=b({name:"",email:"",company:"",phone:"",subject:"",message:""}),l=u(""),p=u(""),h=async()=>{c.value=!0,l.value="";try{await new Promise(e=>setTimeout(e,2e3)),Object.keys(a).forEach(e=>{a[e]=""}),l.value="Message sent successfully! We'll get back to you soon.",p.value="success",setTimeout(()=>{l.value=""},5e3)}catch{l.value="Error sending message. Please try again.",p.value="error"}finally{c.value=!1}};return(e,o)=>(m(),d("div",w,[t("section",V,[t("div",j,[t("div",M,[t("h1",S,s(e.$t("contact.title")),1),t("p",q,s(e.$t("contact.subtitle")),1)])])]),t("section",C,[t("div",T,[t("div",U,[t("div",E,[t("h2",null,s(e.$t("contact.form.send")),1),t("form",{onSubmit:g(h,["prevent"]),class:"contact-form"},[t("div",P,[t("label",B,s(e.$t("contact.form.name"))+" *",1),i(t("input",{type:"text",id:"name","onUpdate:modelValue":o[0]||(o[0]=n=>a.name=n),required:"",class:"form-input",placeholder:e.$t("contact.form.name")},null,8,D),[[r,a.name]])]),t("div",I,[t("label",N,s(e.$t("contact.form.email"))+" *",1),i(t("input",{type:"email",id:"email","onUpdate:modelValue":o[1]||(o[1]=n=>a.email=n),required:"",class:"form-input",placeholder:e.$t("contact.form.email")},null,8,R),[[r,a.email]])]),t("div",z,[t("label",A,s(e.$t("contact.form.company")),1),i(t("input",{type:"text",id:"company","onUpdate:modelValue":o[2]||(o[2]=n=>a.company=n),class:"form-input",placeholder:e.$t("contact.form.company")},null,8,F),[[r,a.company]])]),t("div",G,[t("label",L,s(e.$t("contact.form.phone")),1),i(t("input",{type:"tel",id:"phone","onUpdate:modelValue":o[3]||(o[3]=n=>a.phone=n),class:"form-input",placeholder:e.$t("contact.form.phone")},null,8,O),[[r,a.phone]])]),t("div",W,[t("label",H,s(e.$t("contact.form.subject"))+" *",1),i(t("select",{id:"subject","onUpdate:modelValue":o[4]||(o[4]=n=>a.subject=n),required:"",class:"form-select"},[t("option",J,s(e.$t("contact.form.subject")),1),o[6]||(o[6]=f('<option value="general" data-v-23c59f2e>General Inquiry</option><option value="demo" data-v-23c59f2e>Request Demo</option><option value="partnership" data-v-23c59f2e>Partnership</option><option value="support" data-v-23c59f2e>Technical Support</option><option value="pricing" data-v-23c59f2e>Pricing Information</option>',5))],512),[[y,a.subject]])]),t("div",K,[t("label",Q,s(e.$t("contact.form.message"))+" *",1),i(t("textarea",{id:"message","onUpdate:modelValue":o[5]||(o[5]=n=>a.message=n),required:"",class:"form-textarea",rows:"5",placeholder:e.$t("contact.form.message")},null,8,X),[[r,a.message]])]),t("button",{type:"submit",class:"btn btn-primary form-submit",disabled:c.value},[c.value?(m(),d("span",x,s(e.$t("contact.form.sending")),1)):(m(),d("span",Z,s(e.$t("contact.form.send")),1)),c.value?v("",!0):(m(),d("svg",tt,o[7]||(o[7]=[t("path",{d:"M22 2L11 13M22 2l-7 20-4-9-9-4 20-7z",stroke:"currentColor","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"},null,-1)])))],8,Y),l.value?(m(),d("div",{key:0,class:$(["submit-message",p.value])},s(l.value),3)):v("",!0)],32)]),t("div",ot,[t("h2",null,s(e.$t("nav.contact")),1),t("div",et,[t("div",st,[o[10]||(o[10]=t("div",{class:"method-icon"},"📧",-1)),t("div",at,[o[8]||(o[8]=t("h3",null,"Email",-1)),t("p",null,s(e.$t("contact.info.email")),1),o[9]||(o[9]=t("p",null,"<EMAIL>",-1))])]),t("div",nt,[o[12]||(o[12]=t("div",{class:"method-icon"},"📞",-1)),t("div",lt,[o[11]||(o[11]=t("h3",null,"Phone",-1)),t("p",null,s(e.$t("contact.info.phone")),1),t("p",null,s(e.$t("contact.info.hours")),1)])]),t("div",it,[o[14]||(o[14]=t("div",{class:"method-icon"},"📍",-1)),t("div",ct,[o[13]||(o[13]=t("h3",null,"Address",-1)),t("p",null,s(e.$t("contact.info.address")),1)])])]),o[15]||(o[15]=f('<div class="response-time" data-v-23c59f2e><h3 data-v-23c59f2e>Response Time</h3><div class="response-stats" data-v-23c59f2e><div class="response-stat" data-v-23c59f2e><span class="stat-value" data-v-23c59f2e>&lt; 2h</span><span class="stat-label" data-v-23c59f2e>Average Response</span></div><div class="response-stat" data-v-23c59f2e><span class="stat-value" data-v-23c59f2e>24/7</span><span class="stat-label" data-v-23c59f2e>Emergency Support</span></div></div></div>',1))])])])])]))}}),pt=k(dt,[["__scopeId","data-v-23c59f2e"]]);export{pt as default};
